{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\src\\views\\product\\addProduct\\index.vue?vue&type=template&id=3968db4b&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\src\\views\\product\\addProduct\\index.vue", "mtime": 1750500332795}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    { staticClass: \"divBox\" },\n    [\n      _c(\n        \"el-card\",\n        { staticClass: \"box-card\" },\n        [\n          _c(\n            \"div\",\n            { staticClass: \"clearfix\" },\n            [\n              _vm.headTab.length > 0\n                ? _c(\n                    \"el-tabs\",\n                    {\n                      model: {\n                        value: _vm.currentTab,\n                        callback: function ($$v) {\n                          _vm.currentTab = $$v\n                        },\n                        expression: \"currentTab\",\n                      },\n                    },\n                    _vm._l(_vm.headTab, function (item, index) {\n                      return _c(\"el-tab-pane\", {\n                        key: index,\n                        attrs: { name: item.name, label: item.title },\n                      })\n                    }),\n                    1\n                  )\n                : _vm._e(),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"el-form\",\n            {\n              directives: [\n                {\n                  name: \"loading\",\n                  rawName: \"v-loading\",\n                  value: _vm.fullscreenLoading,\n                  expression: \"fullscreenLoading\",\n                },\n              ],\n              key: _vm.currentTab,\n              ref: \"formValidate\",\n              staticClass: \"formValidate mt20\",\n              attrs: {\n                rules: _vm.ruleValidate,\n                model: _vm.formValidate,\n                \"label-width\": \"130px\",\n              },\n              nativeOn: {\n                submit: function ($event) {\n                  $event.preventDefault()\n                },\n              },\n            },\n            [\n              _vm.currentTab === \"1\"\n                ? _c(\"productInfo\", {\n                    attrs: {\n                      formValidate: _vm.formValidate,\n                      OneattrValue: _vm.OneattrValue,\n                      attrValue: _vm.attrValue,\n                      is_timed: _vm.is_timed,\n                      timeVal: _vm.timeVal,\n                      videoLink: _vm.videoLink,\n                      timeVal2: _vm.timeVal2,\n                      props: _vm.props,\n                    },\n                    on: {\n                      changeTimed: _vm.switchTimed,\n                      productCon: _vm.productCon,\n                      getSpecsLst: _vm.getSpecsLst,\n                      getSpecsList: _vm.getSpecsList,\n                      generateHeader: _vm.generateHeader,\n                    },\n                  })\n                : _vm._e(),\n              _vm._v(\" \"),\n              _vm.currentTab === \"2\" && _vm.formValidate.type !== 4\n                ? _c(\"productSpecs\", {\n                    ref: \"productSpecs\",\n                    attrs: {\n                      formValidate: _vm.formValidate,\n                      attrs: _vm.attrs,\n                      oneFormBatch: _vm.oneFormBatch,\n                      OneattrValue: _vm.OneattrValue,\n                      ManyAttrValue: _vm.ManyAttrValue,\n                      changeAttrValue: _vm.changeAttrValue,\n                      attrValue: _vm.attrValue,\n                      formThead: _vm.formThead,\n                      formDynamic: _vm.formDynamic,\n                      product_id: _vm.product_id,\n                      cdkeyLibraryList: _vm.cdkeyLibraryList,\n                    },\n                    on: {\n                      handleBlur: _vm.handleBlur,\n                      handleFocus: _vm.handleFocus,\n                      generateAttr: _vm.generateAttr,\n                      handleAddRole: _vm.handleAddRole,\n                      handleRemoveRole: _vm.handleRemoveRole,\n                      attrChangeValue: _vm.attrChangeValue,\n                      batchDel: _vm.batchDel,\n                      getSelectedLiarbry: _vm.getSelectedLiarbry,\n                      delAttrTable: _vm.delAttrTable,\n                      delManyImg: _vm.delManyImg,\n                      attrDetailChangeValue: _vm.attrDetailChangeValue,\n                      setAttrs: _vm.setAttrs,\n                      addVirtual: _vm.addVirtual,\n                      seeVirtual: _vm.seeVirtual,\n                    },\n                  })\n                : _vm._e(),\n              _vm._v(\" \"),\n              _vm.currentTab === \"2\" && _vm.formValidate.type === 4\n                ? _c(\"reservationSpecs\", {\n                    ref: \"reservationSpecs\",\n                    attrs: {\n                      formValidate: _vm.formValidate,\n                      attrs: _vm.attrs,\n                      OneattrValue: _vm.OneattrValue,\n                      ManyAttrValue: _vm.ManyAttrValue,\n                      changeAttrValue: _vm.changeAttrValue,\n                      attrValue: _vm.attrValue,\n                      formThead: _vm.formThead,\n                      oneFormBatch: _vm.oneFormBatch,\n                      formDynamic: _vm.formDynamic,\n                      product_id: _vm.product_id,\n                      cdkeyLibraryList: _vm.cdkeyLibraryList,\n                    },\n                    on: {\n                      generateAttr: _vm.generateAttr,\n                      handleAddRole: _vm.handleAddRole,\n                      handleRemoveRole: _vm.handleRemoveRole,\n                      delAttrTable: _vm.delAttrTable,\n                      delManyImg: _vm.delManyImg,\n                      attrChangeValue: _vm.attrChangeValue,\n                      batchDel: _vm.batchDel,\n                      attrDetailChangeValue: _vm.attrDetailChangeValue,\n                      getSelectedLiarbry: _vm.getSelectedLiarbry,\n                      setAttrs: _vm.setAttrs,\n                      handleBlur: _vm.handleBlur,\n                      handleFocus: _vm.handleFocus,\n                    },\n                  })\n                : _vm._e(),\n              _vm._v(\" \"),\n              _vm.currentTab === \"7\"\n                ? _c(\"reservationSetting\", {\n                    ref: \"reservationSetting\",\n                    attrs: {\n                      roterPre: _vm.roterPre,\n                      formValidate: _vm.formValidate,\n                      formList: _vm.formList,\n                      formUrl: _vm.formUrl,\n                    },\n                    on: {\n                      getFormList: _vm.getFormList,\n                      getFormInfo: _vm.getFormInfo,\n                    },\n                  })\n                : _vm._e(),\n              _vm._v(\" \"),\n              _vm.currentTab === \"3\"\n                ? _c(\"productDetail\", {\n                    attrs: { formValidate: _vm.formValidate },\n                    on: { getEditorContent: _vm.getEditorContent },\n                  })\n                : _vm._e(),\n              _vm._v(\" \"),\n              _vm.currentTab === \"4\"\n                ? _c(\"productMarket\", {\n                    attrs: {\n                      formValidate: _vm.formValidate,\n                      \"good-list\": _vm.goodList,\n                      OneattrValue: _vm.OneattrValue,\n                      open_svip: _vm.open_svip,\n                      manyTabDate: _vm.manyTabDate,\n                      deduction_set: _vm.deduction_set,\n                      extensionStatus: _vm.extensionStatus,\n                      deductionStatus: _vm.deductionStatus,\n                      deduction_ratio_rate: _vm.deduction_ratio_rate,\n                      extension_two_rate: _vm.extension_two_rate,\n                      manyTabTit: _vm.manyTabTit,\n                      ManyAttrValue: _vm.ManyAttrValue,\n                      svip_rate: _vm.svip_rate,\n                      \"base-url\": _vm.baseURL,\n                      specValue: _vm.specValue,\n                      formThead: _vm.formThead,\n                    },\n                    on: { openRecommend: _vm.openRecommend },\n                  })\n                : _vm._e(),\n              _vm._v(\" \"),\n              _vm.currentTab === \"5\"\n                ? _c(\"product-param\", {\n                    attrs: {\n                      formValidate: _vm.formValidate,\n                      customSpecs: _vm.customSpecs,\n                      sysSpecsSelect: _vm.sysSpecsSelect,\n                      merSpecsSelect: _vm.merSpecsSelect,\n                    },\n                    on: { getSpecsList: _vm.getSpecsList },\n                  })\n                : _vm._e(),\n              _vm._v(\" \"),\n              _vm.currentTab === \"6\" && _vm.formValidate.type !== 4\n                ? _c(\"productOther\", {\n                    attrs: {\n                      formValidate: _vm.formValidate,\n                      deliveryList: _vm.deliveryList,\n                      shippingList: _vm.shippingList,\n                      guaranteeList: _vm.guaranteeList,\n                      formList: _vm.formList,\n                      formUrl: _vm.formUrl,\n                      roterPre: _vm.roterPre,\n                    },\n                    on: {\n                      addTem: _vm.addTem,\n                      getFormList: _vm.getFormList,\n                      getFormInfo: _vm.getFormInfo,\n                      addServiceTem: _vm.addServiceTem,\n                    },\n                  })\n                : _vm._e(),\n              _vm._v(\" \"),\n              _vm.currentTab === \"6\" && _vm.formValidate.type === 4\n                ? _c(\"reservationOther\", {\n                    ref: \"reservationOther\",\n                    attrs: {\n                      formValidate: _vm.formValidate,\n                      deliveryList: _vm.deliveryList,\n                      shippingList: _vm.shippingList,\n                      guaranteeList: _vm.guaranteeList,\n                      formList: _vm.formList,\n                      formUrl: _vm.formUrl,\n                      roterPre: _vm.roterPre,\n                    },\n                    on: {\n                      addTem: _vm.addTem,\n                      getFormList: _vm.getFormList,\n                      getFormInfo: _vm.getFormInfo,\n                      addServiceTem: _vm.addServiceTem,\n                    },\n                  })\n                : _vm._e(),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n      _vm._v(\" \"),\n      _c(\n        \"div\",\n        { staticClass: \"footer\" },\n        [\n          _c(\n            \"el-button\",\n            {\n              directives: [\n                {\n                  name: \"show\",\n                  rawName: \"v-show\",\n                  value: _vm.currentTab > 1,\n                  expression: \"currentTab > 1\",\n                },\n              ],\n              staticClass: \"submission\",\n              attrs: { size: \"small\" },\n              on: { click: _vm.handleSubmitUp },\n            },\n            [_vm._v(_vm._s(_vm.$t(\"上一步\")))]\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"el-button\",\n            {\n              directives: [\n                {\n                  name: \"show\",\n                  rawName: \"v-show\",\n                  value: _vm.currentTab != 6,\n                  expression: \"currentTab != 6\",\n                },\n              ],\n              staticClass: \"submission\",\n              attrs: { size: \"small\" },\n              on: {\n                click: function ($event) {\n                  return _vm.handleSubmitNest(\"formValidate\")\n                },\n              },\n            },\n            [_vm._v(_vm._s(_vm.$t(\"下一步\")))]\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"el-button\",\n            {\n              directives: [\n                {\n                  name: \"show\",\n                  rawName: \"v-show\",\n                  value: _vm.currentTab == \"6\" || _vm.$route.query.id,\n                  expression: \"currentTab == '6' || $route.query.id\",\n                },\n              ],\n              staticClass: \"submission\",\n              attrs: { loading: _vm.loading, type: \"primary\", size: \"small\" },\n              on: {\n                click: function ($event) {\n                  return _vm.handleSubmit(\"formValidate\")\n                },\n              },\n            },\n            [_vm._v(_vm._s(_vm.$t(\"提交\")))]\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"el-button\",\n            {\n              staticClass: \"submission\",\n              attrs: { loading: _vm.loading, size: \"small\" },\n              on: {\n                click: function ($event) {\n                  return _vm.handlePreview(\"formValidate\")\n                },\n              },\n            },\n            [_vm._v(_vm._s(_vm.$t(\"预览\")))]\n          ),\n        ],\n        1\n      ),\n      _vm._v(\" \"),\n      _vm.attrShow\n        ? _c(\n            \"el-dialog\",\n            {\n              attrs: {\n                visible: _vm.attrShow,\n                title: _vm.$t(\"请选择商品规格\"),\n                width: \"320px\",\n              },\n              on: {\n                \"update:visible\": function ($event) {\n                  _vm.attrShow = $event\n                },\n              },\n            },\n            [\n              _vm.attrShow\n                ? _c(\"attr-list\", {\n                    attrs: { attrs: _vm.attrsList },\n                    on: {\n                      activeData: _vm.activeAttr,\n                      close: _vm.labelAttr,\n                      subAttrs: _vm.subAttrs,\n                    },\n                  })\n                : _vm._e(),\n            ],\n            1\n          )\n        : _vm._e(),\n      _vm._v(\" \"),\n      _c(\"guarantee-service\", {\n        ref: \"serviceGuarantee\",\n        on: { \"get-list\": _vm.getGuaranteeList },\n      }),\n      _vm._v(\" \"),\n      _vm.previewVisible\n        ? _c(\n            \"div\",\n            [\n              _c(\"div\", {\n                staticClass: \"bg\",\n                on: {\n                  click: function ($event) {\n                    $event.stopPropagation()\n                    _vm.previewVisible = false\n                  },\n                },\n              }),\n              _vm._v(\" \"),\n              _vm.previewVisible\n                ? _c(\"preview-box\", {\n                    ref: \"previewBox\",\n                    attrs: { \"preview-key\": _vm.previewKey },\n                  })\n                : _vm._e(),\n            ],\n            1\n          )\n        : _vm._e(),\n      _vm._v(\" \"),\n      _c(\"tao-bao\", {\n        ref: \"taoBao\",\n        on: {\n          \"info-data\": function ($event) {\n            return _vm.infoData($event, \"taobao\")\n          },\n        },\n      }),\n      _vm._v(\" \"),\n      _c(\"add-carMy\", {\n        ref: \"addCarMy\",\n        attrs: { virtualList: _vm.virtualList },\n        on: {\n          changeVirtual: _vm.changeVirtual,\n          fixdBtn: _vm.fixdBtn,\n          closeCarMy: _vm.closeCarMy,\n        },\n      }),\n      _vm._v(\" \"),\n      _c(\"cdkey-library\", {\n        ref: \"cdkeyLibrary\",\n        attrs: {\n          cdkeyLibraryInfo: _vm.cdkeyLibraryInfo,\n          selectedLibrary: _vm.selectedLibrary,\n        },\n        on: { handlerSubSuccess: _vm.handlerChangeCdkeyIdSubSuccess },\n      }),\n      _vm._v(\" \"),\n      _c(\n        \"el-dialog\",\n        {\n          attrs: {\n            visible: _vm.recommendVisible,\n            title: _vm.$t(\"推荐商品列表\"),\n            width: \"900px\",\n          },\n          on: {\n            \"update:visible\": function ($event) {\n              _vm.recommendVisible = $event\n            },\n          },\n        },\n        [\n          _vm.recommendVisible\n            ? _c(\"goods-list\", {\n                ref: \"goodslist\",\n                attrs: {\n                  ischeckbox: true,\n                  isGood: true,\n                  product_id: _vm.product_id,\n                  selectedArr: _vm.goodList,\n                },\n                on: {\n                  getProductId: _vm.getRecommend,\n                  close: _vm.closeRecommend,\n                },\n              })\n            : _vm._e(),\n        ],\n        1\n      ),\n      _vm._v(\" \"),\n      _c(\"templatesFrom\", {\n        ref: \"templateForm\",\n        on: { getList: _vm.getShippingList },\n      }),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}