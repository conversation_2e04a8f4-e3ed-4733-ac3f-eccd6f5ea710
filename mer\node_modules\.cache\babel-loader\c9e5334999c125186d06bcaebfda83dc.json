{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\src\\views\\product\\addProduct\\components\\productSpecs.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\src\\views\\product\\addProduct\\components\\productSpecs.vue", "mtime": 1750510846962}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\.babelrc", "mtime": 1749087282000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\babel.config.js", "mtime": 1735790252000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"C:/Users/<USER>/Desktop/crmeb/view/merPC/mer/node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _defineProperty2 = _interopRequireDefault(require(\"C:/Users/<USER>/Desktop/crmeb/view/merPC/mer/node_modules/@babel/runtime/helpers/defineProperty.js\"));\nvar _toArray2 = _interopRequireDefault(require(\"C:/Users/<USER>/Desktop/crmeb/view/merPC/mer/node_modules/@babel/runtime/helpers/toArray.js\"));\nvar _toConsumableArray2 = _interopRequireDefault(require(\"C:/Users/<USER>/Desktop/crmeb/view/merPC/mer/node_modules/@babel/runtime/helpers/toConsumableArray.js\"));\nrequire(\"core-js/modules/es6.array.find-index\");\nrequire(\"core-js/modules/web.dom.iterable\");\nrequire(\"core-js/modules/es6.string.iterator\");\nrequire(\"core-js/modules/es6.set\");\nvar _createForOfIteratorHelper2 = _interopRequireDefault(require(\"C:/Users/<USER>/Desktop/crmeb/view/merPC/mer/node_modules/@babel/runtime/helpers/createForOfIteratorHelper.js\"));\nrequire(\"core-js/modules/es6.number.constructor\");\nrequire(\"core-js/modules/es6.function.name\");\nrequire(\"core-js/modules/es6.array.find\");\nrequire(\"core-js/modules/es6.regexp.to-string\");\nrequire(\"core-js/modules/es6.regexp.search\");\nvar _leaveuKeyTerms = _interopRequireDefault(require(\"@/config/leaveuKeyTerms.js\"));\nvar _vuedraggable = _interopRequireDefault(require(\"vuedraggable\"));\nvar _sortablejs = _interopRequireDefault(require(\"sortablejs\"));\nvar _product = require(\"@/api/product\");\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default2 = exports.default = {\n  props: {\n    formValidate: {\n      type: Object,\n      default: function _default() {\n        return {\n          spec_type: 0 // 默认值\n        };\n      }\n    },\n    ManyAttrValue: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    },\n    changeAttrValue: {\n      type: String,\n      default: function _default() {\n        return \"\";\n      }\n    },\n    attrValue: {\n      type: Object,\n      default: function _default() {}\n    },\n    formThead: {\n      type: Object,\n      default: function _default() {}\n    },\n    oneFormBatch: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    },\n    OneattrValue: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    },\n    formDynamic: {\n      type: Object,\n      default: function _default() {\n        return {};\n      }\n    },\n    product_id: {\n      type: String,\n      default: \"\"\n    },\n    attrs: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    },\n    cdkeyLibraryList: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    },\n    selectedLibrary: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    }\n  },\n  components: {\n    draggable: _vuedraggable.default\n  },\n  data: function data() {\n    return {\n      ruleList: [],\n      timeCheckAll: [],\n      reservationTime: [],\n      timeCheckAllGroup: [],\n      //自动划分当前选中的元素\n      timeCheckAllClone: [],\n      //自动划分克隆全部选中的元素\n      timeDataClone: [],\n      //自定义划分时的库存（为了切换时段划分时，可以复原之前选中的数据）\n      canSel: true,\n      // 规格图片添加判断\n      tableKey: 0,\n      selectRule: \"\",\n      // 开发者选项控制\n      showDeveloperOptions: false,\n      // 服务包相关数据（默认展开状态）\n      showAdvancedPackages: true,\n      packageConfig: {\n        basic: {\n          enabled: true,\n          name: 'Basic',\n          price: 0,\n          delivery_time: '3天',\n          revisions: '2',\n          description: '',\n          customNote: '',\n          features: {\n            logoDesign: false,\n            logoUsageGuidelines: false,\n            colorPalette: false,\n            typographyGuidelines: false,\n            iconography: false,\n            dosAndDonts: false,\n            brandBookDesign: false\n          }\n        },\n        standard: {\n          enabled: false,\n          name: 'Standard',\n          price: 0,\n          delivery_time: '5天',\n          revisions: '5',\n          description: '',\n          customNote: '',\n          features: {\n            logoDesign: false,\n            logoUsageGuidelines: false,\n            colorPalette: false,\n            typographyGuidelines: false,\n            iconography: false,\n            dosAndDonts: false,\n            brandBookDesign: false\n          }\n        },\n        premium: {\n          enabled: false,\n          name: 'Premium',\n          price: 0,\n          delivery_time: '7天',\n          revisions: '无限',\n          description: '',\n          customNote: '',\n          features: {\n            logoDesign: false,\n            logoUsageGuidelines: false,\n            colorPalette: false,\n            typographyGuidelines: false,\n            iconography: false,\n            dosAndDonts: false,\n            brandBookDesign: false\n          }\n        }\n      },\n      extraServices: {\n        fastDelivery: {\n          enabled: false,\n          name: '加急处理',\n          price: 0,\n          options: []\n        },\n        additionalRevisions: {\n          enabled: false,\n          name: '额外修改',\n          price: 0,\n          options: []\n        },\n        warranty: {\n          enabled: false,\n          name: '延长保障',\n          price: 0,\n          options: []\n        }\n      },\n      // 服务特性配置\n      serviceFeatures: [{\n        key: 'logoDesign',\n        label: 'Includes logo design',\n        tooltip: '包含标志设计服务'\n      }, {\n        key: 'logoUsageGuidelines',\n        label: 'Logo usage guidelines',\n        tooltip: '提供标志使用指南'\n      }, {\n        key: 'colorPalette',\n        label: 'Color palette',\n        tooltip: '提供色彩搭配方案'\n      }, {\n        key: 'typographyGuidelines',\n        label: 'Typography guidelines',\n        tooltip: '提供字体使用指南'\n      }, {\n        key: 'iconography',\n        label: 'Iconography',\n        tooltip: '提供图标设计'\n      }, {\n        key: 'dosAndDonts',\n        label: \"Do's and don'ts\",\n        tooltip: '提供使用规范说明'\n      }, {\n        key: 'brandBookDesign',\n        label: 'Brand book design',\n        tooltip: '提供品牌手册设计'\n      }]\n    };\n  },\n  mounted: function mounted() {\n    this.productGetRule();\n    this.showSpecsByType();\n    this.checkDeveloperMode();\n  },\n  methods: (0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)({\n    // 检查开发者模式\n    checkDeveloperMode: function checkDeveloperMode() {\n      var _this2 = this;\n      // 检查URL参数\n      var urlParams = new URLSearchParams(window.location.search);\n      var isDeveloper = urlParams.get('developer') === 'true';\n\n      // 检查localStorage\n      var localDeveloper = localStorage.getItem('crmeb_developer_mode') === 'true';\n\n      // 检查是否按下了特殊键组合 (Ctrl+Shift+D)\n      document.addEventListener('keydown', function (e) {\n        if (e.ctrlKey && e.shiftKey && e.key === 'D') {\n          _this2.showDeveloperOptions = !_this2.showDeveloperOptions;\n          localStorage.setItem('crmeb_developer_mode', _this2.showDeveloperOptions.toString());\n          _this2.$message.info(_this2.showDeveloperOptions ? '开发者模式已开启' : '开发者模式已关闭');\n        }\n      });\n\n      // 如果URL或localStorage中有开发者模式标记，则显示开发者选项\n      if (isDeveloper || localDeveloper) {\n        this.showDeveloperOptions = true;\n      }\n    },\n    // 切换开发者选项显示\n    toggleDeveloperOptions: function toggleDeveloperOptions() {\n      this.showDeveloperOptions = !this.showDeveloperOptions;\n      localStorage.setItem('crmeb_developer_mode', this.showDeveloperOptions.toString());\n    },\n    // 处理服务特性变化\n    onFeatureChange: function onFeatureChange(packageType, featureKey, value) {\n      this.$set(this.packageConfig[packageType].features, featureKey, value);\n      console.log(\"\".concat(packageType, \" \\u5957\\u9910\\u7684 \").concat(featureKey, \" \\u7279\\u6027\\u8BBE\\u7F6E\\u4E3A: \").concat(value));\n    },\n    /**根据商品类型判断是否显示重量体积 */showSpecsByType: function showSpecsByType() {\n      if (this.formValidate.type == 2 || this.formValidate.type == 3) {\n        delete this.attrValue.weight;\n        delete this.attrValue.volume;\n      } else {\n        this.attrValue.weight = \"\";\n        this.attrValue.volume = \"\";\n      }\n    },\n    // 规格名称改变\n    attrChangeValue: function attrChangeValue(i, val) {\n      this.$emit(\"attrChangeValue\", i, val);\n    },\n    handleChange: function handleChange(event, index, name) {\n      var result = this.cdkeyLibraryList.find(function (item) {\n        return item.id === event;\n      });\n      this.$set(this[name][index], \"stock\", event ? Number(result.total_num - result.used_num) : 0);\n      if (name == \"ManyAttrValue\") this.getSelectedLiarbry(this[name][index], this.ManyAttrValue);\n    },\n    attrDetailChangeValue: function attrDetailChangeValue(val, i) {\n      this.$emit(\"attrDetailChangeValue\", val, i);\n    },\n    //添加云盘链接\n    addVirtual: function addVirtual(type, index, name) {\n      this.$emit(\"addVirtual\", type, index, name);\n    },\n    handleFocus: function handleFocus(val) {\n      this.$emit(\"handleFocus\", val);\n    },\n    handleBlur: function handleBlur() {\n      this.$emit(\"handleBlur\");\n    },\n    // 规格图片添加开关\n    addPic: function addPic(e, i) {\n      var _this3 = this;\n      if (e) {\n        this.attrs.map(function (item, ii) {\n          if (ii !== i) {\n            _this3.$set(item, \"add_pic\", 0);\n          }\n        });\n        this.canSel = false;\n      } else {\n        this.canSel = true;\n      }\n    },\n    handleShowPop: function handleShowPop(index) {\n      this.$refs[\"inputRef_\" + index][0].focus();\n    },\n    // 删除规格\n    handleRemoveRole: function handleRemoveRole(index) {\n      this.$emit(\"handleRemoveRole\", index);\n    },\n    // 删除属性\n    handleRemove2: function handleRemove2(item, index, val) {\n      item.splice(index, 1);\n      this.$emit(\"delAttrTable\", val);\n    },\n    handleSelImg: function handleSelImg(item, index, indexn) {\n      var that = this;\n      this.$modalUpload(function (img) {\n        item.pic = img[0];\n        that.changeSpecImg([item.value], img[0], index, indexn);\n      });\n    },\n    changeSpecImg: function changeSpecImg(arr, img, index, indexn) {\n      var _this4 = this;\n      // 判断是否存在规格图\n      var isHas = false;\n      for (var i = 1; i < this.ManyAttrValue.length; i++) {\n        var item = this.ManyAttrValue[i];\n        if (item.image && this.isSubset(item.attr_arr, arr)) {\n          isHas = true;\n          break;\n        }\n      }\n      if (isHas) {\n        this.$confirm(\"可以同步修改下方该规格图片，确定要替换吗？\", \"提示\", {\n          confirmButtonText: _leaveuKeyTerms.default['替换'],\n          cancelButtonText: _leaveuKeyTerms.default['暂不'],\n          type: \"warning\"\n        }).then(function () {\n          var _iterator = (0, _createForOfIteratorHelper2.default)(_this4.ManyAttrValue),\n            _step;\n          try {\n            for (_iterator.s(); !(_step = _iterator.n()).done;) {\n              var val = _step.value;\n              if (_this4.isSubset(val.attr_arr, arr)) {\n                _this4.$set(val, \"image\", img);\n              }\n            }\n          } catch (err) {\n            _iterator.e(err);\n          } finally {\n            _iterator.f();\n          }\n          _this4.$emit(\"setAttrs\", _this4.attrs);\n        }).catch(function () {});\n      } else {\n        var _iterator2 = (0, _createForOfIteratorHelper2.default)(this.ManyAttrValue),\n          _step2;\n        try {\n          for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {\n            var val = _step2.value;\n            if (this.isSubset(val.attr_arr, arr)) {\n              this.$set(val, \"image\", img);\n            }\n          }\n        } catch (err) {\n          _iterator2.e(err);\n        } finally {\n          _iterator2.f();\n        }\n        this.$emit(\"setAttrs\", this.attrs);\n      }\n    },\n    isSubset: function isSubset(arr1, arr2) {\n      // 将数组转换为 Set，以便进行高效的包含检查\n      var set1 = new Set(arr1);\n      var set2 = new Set(arr2);\n      // 检查 set2 中的每个元素是否都在 set1 中\n      var _iterator3 = (0, _createForOfIteratorHelper2.default)(set2),\n        _step3;\n      try {\n        for (_iterator3.s(); !(_step3 = _iterator3.n()).done;) {\n          var elem = _step3.value;\n          if (!set1.has(elem)) {\n            return false;\n          }\n        }\n      } catch (err) {\n        _iterator3.e(err);\n      } finally {\n        _iterator3.f();\n      }\n      return true;\n    },\n    // 切换默认选中规格\n    changeDefaultSelect: function changeDefaultSelect(e, index) {\n      // 一个开启 其他关闭\n      this.ManyAttrValue.map(function (item, i) {\n        if (i !== index) {\n          item.is_default_select = 0;\n        }\n      });\n      if (e) this.ManyAttrValue[index].is_show = 1;\n    },\n    // 添加属性\n    createAttr: function createAttr(num, idx) {\n      var _this5 = this;\n      if (num) {\n        // 判断是否存在同样熟悉\n        var isExist = this.attrs[idx].detail.some(function (item) {\n          return item.value === num;\n        });\n        if (isExist) {\n          this.$message.error(\"规格值已存在\");\n          return;\n        }\n        this.attrs[idx].detail.push({\n          value: num,\n          image: \"\"\n        });\n        this.formValidate.attr = this.attrs;\n        if (this.ManyAttrValue.length) {\n          this.addOneAttr(this.attrs[idx].value, num);\n        } else {\n          this.$emit(\"generateAttr\", this.attrs);\n        }\n        this.$refs[\"popoverRef_\" + idx][0].doClose(); //关闭的\n        this.clearAttr();\n        setTimeout(function () {\n          if (_this5.$refs[\"popoverRef_\" + idx]) {\n            //重点是以下两句\n            _this5.$refs[\"popoverRef_\" + idx][0].doShow(); //打开的\n            //重点是以上两句\n          }\n        }, 20);\n      } else {\n        this.$refs[\"popoverRef_\" + idx][0].doClose(); //关闭的\n      }\n      // 监听多规格值变化，在新增时候默认选中规格要自动默认第一个数据\n      var exists = this.ManyAttrValue.some(function (item) {\n        return item.is_default_select == 0;\n      });\n      if (exists) {\n        this.ManyAttrValue[1].is_default_select = 1;\n      }\n    },\n    // 新增一条属性\n    addOneAttr: function addOneAttr(val, val2) {\n      this.$emit(\"generateAttr\", this.attrs, val2);\n    },\n    handleRemoveImg: function handleRemoveImg(val, index, indexn) {\n      this.$emit(\"delManyImg\", val, index, indexn);\n    },\n    clearAttr: function clearAttr() {\n      this.formDynamic.attrsName = \"\";\n      this.formDynamic.attrsVal = \"\";\n    },\n    // 清空批量规格信息\n    batchDel: function batchDel() {\n      this.$emit(\"batchDel\");\n    },\n    // 生成列表 行 列 数据\n    tableCellClassName: function tableCellClassName(_ref) {\n      var row = _ref.row,\n        column = _ref.column,\n        rowIndex = _ref.rowIndex,\n        columnIndex = _ref.columnIndex;\n      //注意这里是解构\n      //利用单元格的 className 的回调方法，给行列索引赋值\n      row.index = rowIndex || \"\";\n      column.index = columnIndex;\n    },\n    handleSaveAsTemplate: function handleSaveAsTemplate() {\n      var _this6 = this;\n      this.$prompt(\"\", \"请输入模板名称\", {\n        confirmButtonText: _leaveuKeyTerms.default['确定'],\n        cancelButtonText: _leaveuKeyTerms.default['取消']\n      }).then(function (_ref2) {\n        var value = _ref2.value;\n        var template_value = _this6.attrs.map(function (item) {\n          return {\n            value: item.value,\n            detail: item.detail.map(function (e) {\n              return e.value;\n            })\n          };\n        });\n        var formDynamic = {\n          template_name: value,\n          template_value: template_value\n        };\n        (0, _product.attrCreatApi)(formDynamic, 0).then(function (res) {\n          _this6.$message.success(res.message);\n          _this6.productGetRule();\n        }).catch(function (res) {\n          _this6.$message.error(res.message);\n        });\n      }).catch(function () {});\n    },\n    // 选择规格\n    onChangeSpec: function onChangeSpec(num) {\n      if (num === 1) this.productGetRule();\n    },\n    changeCurrentIndex: function changeCurrentIndex(i) {\n      this.currentIndex = i;\n    },\n    // 获取商品属性模板；\n    productGetRule: function productGetRule() {\n      var _this7 = this;\n      (0, _product.templateLsitApi)().then(function (res) {\n        _this7.ruleList = res.data;\n      });\n    },\n    // 新增规格\n    handleAddRole: function handleAddRole() {\n      this.$emit(\"handleAddRole\");\n    },\n    // 批量添加\n    batchAdd: function batchAdd() {\n      var arr = [];\n      var _iterator4 = (0, _createForOfIteratorHelper2.default)(this.attrs),\n        _step4;\n      try {\n        for (_iterator4.s(); !(_step4 = _iterator4.n()).done;) {\n          var val = _step4.value;\n          if (this.oneFormBatch[0][val.value]) {\n            arr.push(this.oneFormBatch[0][val.value]);\n          }\n        }\n      } catch (err) {\n        _iterator4.e(err);\n      } finally {\n        _iterator4.f();\n      }\n      var _iterator5 = (0, _createForOfIteratorHelper2.default)(this.ManyAttrValue),\n        _step5;\n      try {\n        for (_iterator5.s(); !(_step5 = _iterator5.n()).done;) {\n          var _val = _step5.value;\n          if (arr.length) {\n            if (this.isSubset(_val.attr_arr, arr)) {\n              if (this.oneFormBatch[0].image) {\n                this.$set(_val, \"image\", this.oneFormBatch[0].image);\n              }\n              if (this.oneFormBatch[0].price != undefined && this.oneFormBatch[0].price != \"\") {\n                this.$set(_val, \"price\", this.oneFormBatch[0].price);\n              }\n              if (this.oneFormBatch[0].cost != undefined && this.oneFormBatch[0].cost != \"\") {\n                this.$set(_val, \"cost\", this.oneFormBatch[0].cost);\n              }\n              if (this.oneFormBatch[0].ot_price != undefined && this.oneFormBatch[0].ot_price != \"\") {\n                this.$set(_val, \"ot_price\", this.oneFormBatch[0].ot_price);\n              }\n              if (this.oneFormBatch[0].stock != undefined && this.oneFormBatch[0].stock != \"\") {\n                this.$set(_val, \"stock\", this.oneFormBatch[0].stock);\n              }\n              if (this.oneFormBatch[0].bar_code != undefined && this.oneFormBatch[0].bar_code != \"\") {\n                this.$set(_val, \"bar_code\", this.oneFormBatch[0].bar_code);\n              }\n              if (this.oneFormBatch[0].bar_code_number != undefined && this.oneFormBatch[0].bar_code_number != \"\") {\n                this.$set(_val, \"bar_code_number\", this.oneFormBatch[0].bar_code_number);\n              }\n              if (this.oneFormBatch[0].weight != undefined && this.oneFormBatch[0].weight != \"\") {\n                this.$set(_val, \"weight\", this.oneFormBatch[0].weight);\n              }\n              if (this.oneFormBatch[0].volume != undefined && this.oneFormBatch[0].volume != \"\") {\n                this.$set(_val, \"volume\", this.oneFormBatch[0].volume);\n              }\n            }\n          } else {\n            if (this.oneFormBatch[0].image) {\n              this.$set(_val, \"image\", this.oneFormBatch[0].image);\n            }\n            if (this.oneFormBatch[0].price != undefined && this.oneFormBatch[0].price != \"\") {\n              this.$set(_val, \"price\", this.oneFormBatch[0].price);\n            }\n            if (this.oneFormBatch[0].cost != undefined && this.oneFormBatch[0].cost != \"\") {\n              this.$set(_val, \"cost\", this.oneFormBatch[0].cost);\n            }\n            if (this.oneFormBatch[0].ot_price != undefined && this.oneFormBatch[0].ot_price != \"\") {\n              this.$set(_val, \"ot_price\", this.oneFormBatch[0].ot_price);\n            }\n            if (this.oneFormBatch[0].stock != undefined && this.oneFormBatch[0].stock != \"\") {\n              this.$set(_val, \"stock\", this.oneFormBatch[0].stock);\n            }\n            if (this.oneFormBatch[0].weight != undefined && this.oneFormBatch[0].weight != \"\") {\n              this.$set(_val, \"weight\", this.oneFormBatch[0].weight);\n            }\n            if (this.oneFormBatch[0].volume != undefined && this.oneFormBatch[0].volume != \"\") {\n              this.$set(_val, \"volume\", this.oneFormBatch[0].volume);\n            }\n            if (this.oneFormBatch[0].bar_code != undefined && this.oneFormBatch[0].bar_code != \"\") {\n              this.$set(_val, \"bar_code\", this.oneFormBatch[0].bar_code);\n            }\n            if (this.oneFormBatch[0].bar_code_number != undefined && this.oneFormBatch[0].bar_code_number != \"\") {\n              this.$set(_val, \"bar_code_number\", this.oneFormBatch[0].bar_code_number);\n            }\n          }\n        }\n      } catch (err) {\n        _iterator5.e(err);\n      } finally {\n        _iterator5.f();\n      }\n    },\n    // 合并单元格\n    objectSpanMethod: function objectSpanMethod(_ref3) {\n      var row = _ref3.row,\n        column = _ref3.column,\n        rowIndex = _ref3.rowIndex,\n        columnIndex = _ref3.columnIndex;\n      if (columnIndex === 0 && rowIndex > 0) {\n        var lable = column.label;\n        //这里判断第几列需要合并\n\n        // 添加安全检查，确保数据结构存在\n        if (!this.ManyAttrValue[rowIndex] || !this.ManyAttrValue[rowIndex].detail) {\n          return {\n            rowspan: 1,\n            colspan: 1\n          };\n        }\n        var tagFamily = this.ManyAttrValue[rowIndex].detail[lable];\n        var index = this.ManyAttrValue.findIndex(function (item, index) {\n          if (index > 0 && item && item.detail) {\n            return item.detail[lable] == tagFamily;\n          }\n          return false;\n        });\n        if (rowIndex == index) {\n          var len = 1;\n          for (var i = index + 1; i < this.ManyAttrValue.length; i++) {\n            if (!this.ManyAttrValue[i] || !this.ManyAttrValue[i].detail || this.ManyAttrValue[i].detail[lable] !== tagFamily) {\n              break;\n            }\n            len++;\n          }\n          return {\n            rowspan: len,\n            colspan: 1\n          };\n        } else {\n          return {\n            rowspan: 0,\n            colspan: 0\n          };\n        }\n      }\n    },\n    // 点击商品图\n    modalPicTap: function modalPicTap(tit, num, i) {\n      var _this = this;\n      var attr = [];\n      this.$modalUpload(function (img) {\n        if (tit === \"1\" && !num) {\n          _this.formValidate.image = img[0];\n          _this.OneattrValue[0].image = img[0];\n        }\n        if (tit === \"2\" && !num) {\n          img.map(function (item) {\n            attr.push(item.attachment_src);\n            _this.formValidate.slider_image.push(item);\n            if (_this.formValidate.slider_image.length > 10) {\n              _this.formValidate.slider_image.length = 10;\n            }\n          });\n        }\n        if (tit === \"1\" && num === \"dan\") {\n          _this.OneattrValue[0].image = img[0];\n        }\n        if (tit === \"1\" && num === \"duo\") {\n          _this.ManyAttrValue[i].image = img[0];\n        }\n        if (tit === \"1\" && num === \"pi\") {\n          _this.oneFormBatch[0].image = img[0];\n        }\n      }, tit);\n    },\n    // 规格拖拽排序后\n    onMoveSpec: function onMoveSpec() {\n      this.$emit(\"generateAttr\", this.attrs);\n    },\n    //清空卡密\n    virtualListClear: function virtualListClear() {\n      this.virtualList = [{\n        is_type: 0,\n        key: \"\",\n        stock: \"\"\n      }];\n    },\n    seeVirtual: function seeVirtual(type, data, name, index) {\n      this.$emit(\"seeVirtual\", type, data, name, index);\n    },\n    getSelectedLiarbry: function getSelectedLiarbry(data, array) {\n      this.$emit(\"getSelectedLiarbry\", data, array);\n    }\n  }, \"changeCurrentIndex\", function changeCurrentIndex(i) {\n    this.currentIndex = i;\n  }), \"confirm\", function confirm(name) {\n    var _this8 = this;\n    this.selectRule = name;\n    this.createBnt = true;\n    if (!this.selectRule) {\n      return this.$message.warning(\"请选择属性\");\n    }\n    this.ruleList.forEach(function (item) {\n      if (item.attr_template_id === _this8.selectRule) {\n        item.template_value.forEach(function (value, index) {\n          value.add_pic = 0;\n        });\n        _this8.canSel = true;\n        _this8.$emit(\"setAttrs\", (0, _toConsumableArray2.default)(item.template_value));\n        _this8.formValidate.attr = item.template_value;\n      }\n    });\n    // this.$emit('generateAttr', this.attrs)\n  }), \"generateCombinations\", function generateCombinations(arr) {\n    var _this9 = this;\n    var prefix = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];\n    if (arr.length === 0) {\n      return [prefix];\n    }\n    var _arr = (0, _toArray2.default)(arr),\n      first = _arr[0],\n      rest = _arr.slice(1);\n    return first.detail.flatMap(function (detail) {\n      return _this9.generateCombinations(rest, [].concat((0, _toConsumableArray2.default)(prefix), [detail.value]));\n    });\n  }), \"generateServicePackageSpecs\", function generateServicePackageSpecs() {\n    var _this0 = this;\n    // 验证配置\n    var enabledPackages = [];\n    if (this.packageConfig.basic.enabled) {\n      if (!this.packageConfig.basic.name || this.packageConfig.basic.price <= 0) {\n        return this.$message.error('请完善Basic套餐的名称和价格');\n      }\n      enabledPackages.push({\n        value: this.packageConfig.basic.name,\n        image: ''\n      });\n    }\n    if (this.packageConfig.standard.enabled) {\n      if (!this.packageConfig.standard.name || this.packageConfig.standard.price <= 0) {\n        return this.$message.error('请完善Standard套餐的名称和价格');\n      }\n      enabledPackages.push({\n        value: this.packageConfig.standard.name,\n        image: ''\n      });\n    }\n    if (this.packageConfig.premium.enabled) {\n      if (!this.packageConfig.premium.name || this.packageConfig.premium.price <= 0) {\n        return this.$message.error('请完善Premium套餐的名称和价格');\n      }\n      enabledPackages.push({\n        value: this.packageConfig.premium.name,\n        image: ''\n      });\n    }\n    if (enabledPackages.length === 0) {\n      return this.$message.error('请至少启用一个服务包');\n    }\n\n    // 生成服务包规格数据，使用与原始多规格兼容的格式\n    var attrs = [];\n\n    // 1. 生成主服务包规格\n    attrs.push({\n      value: 'Packages',\n      detail: enabledPackages,\n      add_pic: 0\n    });\n\n    // 2. 生成额外服务规格（恢复原来的设计）\n    if (this.extraServices.fastDelivery.enabled && this.extraServices.fastDelivery.options.length > 0) {\n      // 为每个选项添加一个\"不选择\"的选项，价格为0\n      var deliveryOptions = ['不选择'].concat((0, _toConsumableArray2.default)(this.extraServices.fastDelivery.options));\n      attrs.push({\n        value: 'extra services (交付时间)',\n        detail: deliveryOptions.map(function (option) {\n          return {\n            value: option,\n            image: '',\n            price: option === '不选择' ? 0 : _this0.extraServices.fastDelivery.price || 0\n          };\n        }),\n        add_pic: 0\n      });\n    }\n    if (this.extraServices.additionalRevisions.enabled && this.extraServices.additionalRevisions.options.length > 0) {\n      // 为每个选项添加一个\"不选择\"的选项，价格为0\n      var revisionOptions = ['不选择'].concat((0, _toConsumableArray2.default)(this.extraServices.additionalRevisions.options));\n      attrs.push({\n        value: 'extra services (追加修改次数)',\n        detail: revisionOptions.map(function (option) {\n          return {\n            value: option,\n            image: '',\n            price: option === '不选择' ? 0 : _this0.extraServices.additionalRevisions.price || 0\n          };\n        }),\n        add_pic: 0\n      });\n    }\n    if (this.extraServices.warranty.enabled && this.extraServices.warranty.options.length > 0) {\n      // 为每个选项添加一个\"不选择\"的选项，价格为0\n      var warrantyOptions = ['不选择'].concat((0, _toConsumableArray2.default)(this.extraServices.warranty.options));\n      attrs.push({\n        value: 'extra services (保过期)',\n        detail: warrantyOptions.map(function (option) {\n          return {\n            value: option,\n            image: '',\n            price: option === '不选择' ? 0 : _this0.extraServices.warranty.price || 0\n          };\n        }),\n        add_pic: 0\n      });\n    }\n\n    // 通知父组件更新规格数据，并传递服务包配置和额外服务配置\n    this.$emit('setAttrs', attrs, this.packageConfig, this.extraServices);\n    // 切换到多规格模式以兼容后端\n    this.formValidate.spec_type = 1;\n    this.$message.success('服务包规格生成成功！请查看下方的规格列表。');\n  }), \"previewServicePackage\", function previewServicePackage() {\n    // 预览服务包配置\n    var config = {\n      packages: this.packageConfig,\n      extraServices: this.extraServices\n    };\n    this.$alert(\"<pre>\".concat(JSON.stringify(config, null, 2), \"</pre>\"), '服务包配置预览', {\n      dangerouslyUseHTMLString: true,\n      customClass: 'preview-dialog'\n    });\n  })\n};", null]}