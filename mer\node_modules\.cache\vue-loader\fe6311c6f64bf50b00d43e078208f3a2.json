{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\src\\views\\product\\addProduct\\components\\productSpecs.vue", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\src\\views\\product\\addProduct\\components\\productSpecs.vue", "mtime": 1750506790852}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\crmeb\\view\\merPC\\mer\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["import { render, staticRenderFns } from \"./productSpecs.vue?vue&type=template&id=f7b5f20a&scoped=true\"\nimport script from \"./productSpecs.vue?vue&type=script&lang=js\"\nexport * from \"./productSpecs.vue?vue&type=script&lang=js\"\nimport style0 from \"./productSpecs.vue?vue&type=style&index=0&id=f7b5f20a&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"f7b5f20a\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\crmeb\\\\view\\\\merPC\\\\mer\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('f7b5f20a')) {\n      api.createRecord('f7b5f20a', component.options)\n    } else {\n      api.reload('f7b5f20a', component.options)\n    }\n    module.hot.accept(\"./productSpecs.vue?vue&type=template&id=f7b5f20a&scoped=true\", function () {\n      api.rerender('f7b5f20a', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/views/product/addProduct/components/productSpecs.vue\"\nexport default component.exports"]}